from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os
from pathlib import Path

# 获取项目根目录路径
BASE_DIR = Path(__file__).parent.parent

class Settings(BaseSettings):
    # 基础路径
    BASE_DIR: Path = BASE_DIR
    
    # 数据库配置 - 移除硬编码密码，强制从环境变量获取
    DB_HOST: str = Field(..., env="DB_HOST")
    DB_PORT: int = Field(..., env="DB_PORT")
    DB_USER: str = Field(..., env="DB_USER")
    DB_PASSWORD: str = Field(..., env="DB_PASSWORD")  # 必须从环境变量获取，不提供默认值
    DB_NAME: str = Field(..., env="DB_NAME")
    
    # 应用配置
    APP_NAME: str = "Backend API"
    API_PREFIX: str = "/api"
    DEBUG: bool = Field(False, env="DEBUG")
    ENVIRONMENT: str = Field("production", env="ENVIRONMENT")  # development, production

    # 安全配置
    SECRET_KEY: str = Field(..., env="SECRET_KEY")  # 必须从环境变量获取
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 2  # 2小时，更安全的过期时间
    
    # 文件上传配置
    UPLOAD_DIR: str = Field("uploads", env="UPLOAD_DIR")
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: list = ["jpg", "jpeg", "png", "gif", "webp"]
    
    # CORS配置 - 根据环境动态配置
    @property
    def CORS_ORIGINS(self) -> list:
        """根据环境动态配置CORS源"""
        if self.ENVIRONMENT == "production":
            # 生产环境：只允许生产域名
            return [
                "https://************:3000",  # 生产前端
                "https://************:5173",  # 生产管理后台
                # 在这里添加您的生产域名
                # "https://your-domain.com",
                # "https://admin.your-domain.com"
            ]
        else:
            # 开发环境：允许本地和Tailscale地址
            return [
                "http://localhost:3000",
                "http://localhost:5173",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:5173",
                "http://**************:3000",
                "http://**************:5173",
                # 允许所有本地开发地址
                "http://0.0.0.0:3000",
                "http://0.0.0.0:5173",
            ]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 创建设置实例
settings = Settings()

# 确保上传目录存在
upload_dir = Path(settings.UPLOAD_DIR)
if not upload_dir.exists():
    upload_dir.mkdir(parents=True, exist_ok=True)
