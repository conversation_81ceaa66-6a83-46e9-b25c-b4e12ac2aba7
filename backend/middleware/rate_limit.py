"""
请求速率限制中间件
用于防止API滥用和DDoS攻击
"""
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import redis
from app.config import settings
import logging
import time
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

# 创建Redis连接（如果可用）
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=1, decode_responses=True)
    redis_client.ping()  # 测试连接
    storage_uri = "redis://localhost:6379/1"
    logger.info("使用Redis作为速率限制存储")
except (redis.ConnectionError, redis.TimeoutError):
    # Redis不可用时使用内存存储
    storage_uri = "memory://"
    logger.warning("Redis不可用，使用内存存储进行速率限制")

# 创建限制器
limiter = Limiter(
    key_func=get_remote_address,
    storage_uri=storage_uri
)

# 简单的内存速率限制器
class SimpleRateLimiter:
    def __init__(self):
        self.requests = defaultdict(deque)

    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        now = time.time()
        request_times = self.requests[key]

        # 清理过期的请求记录
        while request_times and request_times[0] <= now - window:
            request_times.popleft()

        # 检查是否超过限制
        if len(request_times) >= limit:
            return False

        # 记录当前请求
        request_times.append(now)
        return True

# 创建简单限制器实例
simple_limiter = SimpleRateLimiter()

# 自定义速率限制异常处理
async def rate_limit_handler(request: Request, exc: RateLimitExceeded):
    """自定义速率限制异常处理"""
    response = JSONResponse(
        status_code=429,
        content={
            "error": "Rate limit exceeded",
            "message": f"请求过于频繁，请稍后再试。限制: {exc.detail}",
            "retry_after": exc.retry_after
        }
    )
    response.headers["Retry-After"] = str(exc.retry_after)
    return response

# 根据不同端点设置不同的速率限制
def get_rate_limit_for_path(path: str) -> tuple:
    """根据路径返回相应的速率限制 (limit, window_seconds)"""

    # 认证相关端点 - 更严格的限制
    if "/api/auth/" in path:
        return (5, 60)  # 登录/注册每分钟5次

    # 文件上传端点 - 限制上传频率
    elif "/api/images/upload" in path:
        return (10, 60)  # 每分钟10次上传

    # 搜索端点 - 中等限制
    elif "/api/search" in path or "/api/blogs" in path:
        return (30, 60)  # 每分钟30次搜索

    # 管理端点 - 中等限制
    elif "/api/admin/" in path:
        return (60, 60)  # 管理操作每分钟60次

    # 公开API端点 - 宽松限制
    elif "/api/" in path:
        return (100, 60)  # 一般API每分钟100次

    # 静态文件和健康检查 - 最宽松
    else:
        return (200, 60)  # 静态资源每分钟200次

class RateLimitMiddleware:
    """速率限制中间件类"""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)

            # 跳过健康检查和静态文件的速率限制
            if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
                await self.app(scope, receive, send)
                return

            # 获取客户端IP
            client_ip = get_remote_address(request)
            path = request.url.path

            # 获取该路径的速率限制
            limit, window = get_rate_limit_for_path(path)

            # 创建限制键
            rate_key = f"{client_ip}:{path}"

            # 检查速率限制
            if not simple_limiter.is_allowed(rate_key, limit, window):
                logger.warning(f"速率限制触发: {client_ip} -> {path} ({limit}/{window}s)")

                # 创建429响应
                response = JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "message": f"请求过于频繁，请稍后再试。限制: {limit}次/{window}秒",
                        "retry_after": window
                    }
                )
                response.headers["Retry-After"] = str(window)
                await response(scope, receive, send)
                return

            logger.debug(f"速率限制检查通过: {client_ip} -> {path} ({limit}/{window}s)")

        await self.app(scope, receive, send)

# 导出限制器和中间件
__all__ = ["limiter", "RateLimitMiddleware", "rate_limit_handler"]
