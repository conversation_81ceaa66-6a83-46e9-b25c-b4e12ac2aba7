#!/bin/bash

echo "🧪 测试API连接和代理配置..."

# 检查服务运行状态
echo "📊 检查服务运行状态..."
echo "后端 (8000): $(lsof -ti:8000 >/dev/null 2>&1 && echo "✅ 运行中" || echo "❌ 未运行")"
echo "前端 (3000): $(lsof -ti:3000 >/dev/null 2>&1 && echo "✅ 运行中" || echo "❌ 未运行")"
echo "管理端 (5173): $(lsof -ti:5173 >/dev/null 2>&1 && echo "✅ 运行中" || echo "❌ 未运行")"
echo ""

# 测试后端健康检查
echo "1. 测试后端健康检查..."
response=$(curl -s -w "%{http_code}" http://localhost:8000/health)
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    echo "✅ 后端健康检查通过 (HTTP $http_code)"
else
    echo "❌ 后端健康检查失败 (HTTP $http_code)"
fi

# 测试博客API
echo "2. 测试博客API..."
response=$(curl -s -w "%{http_code}" http://localhost:8000/api/blogs/)
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    echo "✅ 博客API正常 (HTTP $http_code)"
else
    echo "❌ 博客API异常 (HTTP $http_code)"
fi

# 测试前端服务
echo "3. 测试前端服务..."
response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:3000)
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    echo "✅ 前端服务正常 (HTTP $http_code)"
else
    echo "❌ 前端服务异常 (HTTP $http_code)"
fi

# 测试管理端服务
echo "4. 测试管理端服务..."
response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:5173)
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    echo "✅ 管理端服务正常 (HTTP $http_code)"
else
    echo "❌ 管理端服务异常 (HTTP $http_code)"
fi

# 测试管理端API代理
echo "5. 测试管理端API代理..."
response=$(curl -s -w "%{http_code}" http://localhost:5173/api/blogs/)
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    echo "✅ 管理端API代理正常 (HTTP $http_code)"
else
    echo "❌ 管理端API代理异常 (HTTP $http_code)"
fi

# 测试认证API
echo "6. 测试认证API..."
response=$(curl -s -w "%{http_code}" -X POST http://localhost:8000/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username":"test","password":"test"}')
http_code="${response: -3}"
if [ "$http_code" = "401" ] || [ "$http_code" = "422" ]; then
    echo "✅ 认证API正常 (HTTP $http_code - 预期的认证失败)"
else
    echo "❌ 认证API异常 (HTTP $http_code)"
fi

echo ""
echo "🎯 测试完成"
echo ""
echo "💡 如果有服务未运行，请执行："
echo "   ./start-dev-services.sh"
