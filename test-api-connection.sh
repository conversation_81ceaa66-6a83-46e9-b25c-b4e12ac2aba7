#!/bin/bash

echo "🧪 测试API连接..."

# 测试后端健康检查
echo "1. 测试后端健康检查..."
if curl -s http://localhost:8000/health | grep -q "ok"; then
    echo "✅ 后端健康检查通过"
else
    echo "❌ 后端健康检查失败"
fi

# 测试博客API
echo "2. 测试博客API..."
if curl -s http://localhost:8000/api/blogs/ | grep -q "title"; then
    echo "✅ 博客API正常"
else
    echo "❌ 博客API异常"
fi

# 测试前端API代理
echo "3. 测试前端API代理..."
if curl -s http://localhost:3000/api/blogs/ | grep -q "title"; then
    echo "✅ 前端API代理正常"
else
    echo "❌ 前端API代理异常"
fi

# 测试管理端API代理
echo "4. 测试管理端API代理..."
if curl -s http://localhost:5173/api/blogs/ | grep -q "title"; then
    echo "✅ 管理端API代理正常"
else
    echo "❌ 管理端API代理异常"
fi

echo "🎯 测试完成"
