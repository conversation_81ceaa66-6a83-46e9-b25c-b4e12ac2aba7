#!/bin/bash

# 开发环境服务启动脚本
# 自动检测并启动前端、admin和后端服务

echo "🚀 启动开发环境服务..."

# 检查端口占用情况
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用 ($service_name)"
        echo "正在停止占用端口 $port 的进程..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# 启动后端服务
start_backend() {
    echo "📡 启动后端服务..."
    check_port 8000 "后端"
    
    cd backend
    
    # 检查poetry环境
    if ! command -v poetry &> /dev/null; then
        echo "❌ Poetry未安装，请先安装poetry"
        return 1
    fi
    
    # 启动后端
    echo "启动后端服务在 http://localhost:8000"
    poetry run uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    
    # 等待后端启动
    echo "等待后端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            echo "✅ 后端服务启动成功"
            break
        fi
        sleep 1
        if [ $i -eq 30 ]; then
            echo "❌ 后端服务启动超时"
            return 1
        fi
    done
    
    cd ..
}

# 启动前端服务
start_frontend() {
    echo "🌐 启动前端服务..."
    check_port 3000 "前端"
    
    cd frontend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install
    fi
    
    # 启动前端
    echo "启动前端服务在 http://localhost:3000"
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
}

# 启动管理端服务
start_admin() {
    echo "⚙️  启动管理端服务..."
    check_port 5173 "管理端"
    
    cd admin
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "安装管理端依赖..."
        npm install
    fi
    
    # 启动管理端
    echo "启动管理端服务在 http://localhost:5173"
    npm run dev &
    ADMIN_PID=$!
    
    cd ..
}

# 测试API连接
test_connections() {
    echo "🧪 测试服务连接..."
    
    # 等待所有服务启动
    sleep 5
    
    # 测试后端
    if curl -s http://localhost:8000/health | grep -q "ok"; then
        echo "✅ 后端服务正常"
    else
        echo "❌ 后端服务异常"
    fi
    
    # 测试前端
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        echo "✅ 前端服务正常"
    else
        echo "❌ 前端服务异常"
    fi
    
    # 测试管理端
    if curl -s http://localhost:5173 >/dev/null 2>&1; then
        echo "✅ 管理端服务正常"
    else
        echo "❌ 管理端服务异常"
    fi
    
    # 测试API代理
    if curl -s http://localhost:5173/api/blogs/ >/dev/null 2>&1; then
        echo "✅ 管理端API代理正常"
    else
        echo "❌ 管理端API代理异常"
    fi
}

# 清理函数
cleanup() {
    echo "🛑 停止所有服务..."
    
    # 停止所有相关进程
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    
    # 停止特定端口的进程
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    
    echo "✅ 所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主执行流程
main() {
    echo "🔧 开发环境配置检查..."
    
    # 检查必要的命令
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm未安装"
        exit 1
    fi
    
    # 启动服务
    start_backend
    if [ $? -ne 0 ]; then
        echo "❌ 后端启动失败"
        exit 1
    fi
    
    start_frontend
    start_admin
    
    # 测试连接
    test_connections
    
    echo ""
    echo "🎉 所有服务启动完成！"
    echo ""
    echo "📋 服务地址："
    echo "- 后端API: http://localhost:8000"
    echo "- 前端网站: http://localhost:3000"
    echo "- 管理后台: http://localhost:5173"
    echo ""
    echo "📖 API文档: http://localhost:8000/docs"
    echo ""
    echo "💡 按 Ctrl+C 停止所有服务"
    
    # 保持脚本运行
    wait
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
