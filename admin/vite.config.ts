import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path';
import { fileURLToPath } from 'url'; // 导入 fileURLToPath

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0', // 监听所有地址，允许通过IP访问
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000', // 开发环境固定使用localhost
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'), // 使用修正后的 __dirname
    },
  },
  build: {
    // 生产环境优化
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        // 生产环境移除console
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        // 分包策略
        manualChunks: {
          // 将React相关库打包到一个chunk
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          // 将Antd相关库打包到一个chunk
          'antd-vendor': ['antd', '@ant-design/icons'],
          // 将工具库打包到一个chunk
          'utils-vendor': ['axios', 'dayjs', 'lodash-es'],
          // 将查询库打包到一个chunk
          'query-vendor': ['@tanstack/react-query'],
        },
      },
    },
    // 设置chunk大小警告阈值
    chunkSizeWarningLimit: 1000,
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      '@ant-design/icons',
      'axios',
      '@tanstack/react-query',
    ],
  },
})
